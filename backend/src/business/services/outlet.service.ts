import { outletRepository } from '../../data/repositories/outlet.repository';
import { type CreateOutletRequest, type UpdateOutletRequest, type OutletQuery, type BulkOutletAction, type OutletStatus } from '../validators/outlet.validator';
import { apiLogger } from '../../infrastructure/logger/pino';
import { AppError, NotFoundError, ConflictError, BadRequestError } from '../../infrastructure/errors';
import {
  IOutletRepository,
  type Outlet,
  type ActiveOutlet,
  type OutletQueryParams,
  type CreateOutletData,
  type UpdateOutletData,
  type PaginatedResult,
} from '../../business/interfaces/repositories/IOutletRepository';

export class OutletService {
  constructor(private readonly outletRepo: IOutletRepository = outletRepository) { }

  async getAll(query: OutletQuery): Promise<PaginatedResult<Outlet>> {
    type FilterType = 'rating' | 'price' | 'cuisine' | 'search' | 'status';

    const logContext = {
      query,
      appliedFilters: {
        hasAdvancedFilters: !!(query.minRating || query.maxPrice || query.cuisine),
        filterTypes: [] as FilterType[],
      },
    };

    // Track which filters are being used
    if (query.minRating) logContext.appliedFilters.filterTypes.push('rating');
    if (query.maxPrice) logContext.appliedFilters.filterTypes.push('price');
    if (query.cuisine) logContext.appliedFilters.filterTypes.push('cuisine');
    if (query.search) logContext.appliedFilters.filterTypes.push('search');
    if (query.isActive !== undefined) logContext.appliedFilters.filterTypes.push('status');

    apiLogger.info('Fetching outlets with filters', logContext);

    try {
      // Validate query parameters
      this.validateQueryParams(query);

      // Convert OutletQuery to OutletQueryParams
      const queryParams: OutletQueryParams = {
        ...(query.isActive !== undefined && { isActive: query.isActive }),
        ...(query.search && { search: query.search }),
        ...(query.sortBy && { sortBy: query.sortBy as 'name' | 'createdAt' | 'updatedAt' }),
        ...(query.sortOrder && { sortOrder: query.sortOrder }),
        ...(query.page && { page: query.page }),
        ...(query.limit && { limit: query.limit }),
        ...(query.minRating && { minRating: query.minRating }),
        ...(query.maxPrice && { maxPrice: query.maxPrice }),
        ...(query.cuisine && { cuisine: query.cuisine }),
      };

      const result = await this.outletRepo.findAll(queryParams);

      apiLogger.info('Successfully retrieved outlets', {
        totalRecords: result.pagination.total,
        appliedFilters: logContext.appliedFilters,
      });

      return result;
    } catch (error) {
      apiLogger.error('Error fetching outlets:', {
        error,
        query: logContext.query,
        appliedFilters: logContext.appliedFilters,
      });
      throw new AppError('Failed to retrieve outlets.');
    }
  }

  private validateQueryParams(query: OutletQuery): void {
    // Validate pagination parameters
    if (query.page && query.page < 1) {
      throw new BadRequestError('Page must be greater than 0');
    }
    if (query.limit && (query.limit < 1 || query.limit > 100)) {
      throw new BadRequestError('Limit must be between 1 and 100');
    }

    // Validate advanced filter parameters
    if (query.minRating !== undefined) {
      if (query.minRating < 0 || query.minRating > 5) {
        throw new BadRequestError('Rating must be between 0 and 5');
      }
    }

    if (query.maxPrice !== undefined) {
      if (query.maxPrice <= 0) {
        throw new BadRequestError('Maximum price must be greater than 0');
      }
    }

    if (query.cuisine !== undefined) {
      if (query.cuisine.trim().length === 0) {
        throw new BadRequestError('Cuisine cannot be empty');
      }
      if (query.cuisine.length > 100) {
        throw new BadRequestError('Cuisine name is too long');
      }
    }

    // Validate sort parameters
    if (query.sortBy && !['name', 'createdAt', 'updatedAt'].includes(query.sortBy)) {
      throw new BadRequestError('Invalid sort field');
    }
    if (query.sortOrder && !['asc', 'desc'].includes(query.sortOrder)) {
      throw new BadRequestError('Sort order must be either "asc" or "desc"');
    }
  }

  async getById(id: number): Promise<Outlet> {
    apiLogger.info('Fetching outlet by ID', { outletId: id });
    const outlet = await this.outletRepo.findById(id);
    if (!outlet) {
      throw new NotFoundError('Outlet not found');
    }
    return outlet;
  }

  async create(outletData: CreateOutletRequest): Promise<Outlet> {
    apiLogger.info('Creating new outlet', { name: outletData.name });

    const existingOutlet = await this.outletRepo.findByName(outletData.name);
    if (existingOutlet) {
      throw new ConflictError('Outlet with this name already exists');
    }

    try {
      // Convert CreateOutletRequest to CreateOutletData
      const createData: CreateOutletData = {
        name: outletData.name,
        address: outletData.address,
        phone: outletData.phoneNumber,
        ...(outletData.description && { description: outletData.description }),
        isActive: outletData.isActive,
      };

      const outlet = await this.outletRepo.create(createData);
      apiLogger.info('Outlet created successfully', { outletId: outlet.outletId });
      return outlet;
    } catch (error) {
      apiLogger.error('Error creating outlet:', error);
      throw new AppError('Failed to create outlet.');
    }
  }

  async update(id: number, updateData: UpdateOutletRequest): Promise<Outlet> {
    apiLogger.info('Updating outlet', { outletId: id, updateData });

    const existingOutlet = await this.outletRepo.findById(id);
    if (!existingOutlet) {
      throw new NotFoundError('Outlet not found');
    }

    if (updateData.name && updateData.name !== existingOutlet.name) {
      const nameExists = await this.outletRepo.nameExists(updateData.name, id);
      if (nameExists) {
        throw new ConflictError('Outlet with this name already exists');
      }
    }

    // Convert UpdateOutletRequest to UpdateOutletData
    const updateDataConverted: Partial<UpdateOutletData> = {
      ...(updateData.name && { name: updateData.name }),
      ...(updateData.address && { address: updateData.address }),
      ...(updateData.phoneNumber && { phone: updateData.phoneNumber }),
      ...(updateData.description !== undefined && { description: updateData.description }),
      ...(updateData.isActive !== undefined && { isActive: updateData.isActive }),
    };

    const updatedOutlet = await this.outletRepo.update(id, updateDataConverted);
    if (!updatedOutlet) {
      throw new AppError('Failed to update outlet.');
    }

    apiLogger.info('Outlet updated successfully', { outletId: id });
    return updatedOutlet;
  }

  async delete(id: number): Promise<void> {
    apiLogger.info('Soft deleting outlet', { outletId: id });
    const success = await this.outletRepo.softDelete(id);
    if (!success) {
      throw new NotFoundError('Outlet not found or already deleted.');
    }
    apiLogger.info('Outlet soft deleted successfully', { outletId: id });
  }

  async restore(id: number): Promise<void> {
    apiLogger.info('Restoring outlet', { outletId: id });
    const success = await this.outletRepo.restore(id);
    if (!success) {
      throw new NotFoundError('Outlet not found or not deleted.');
    }
    apiLogger.info('Outlet restored successfully', { outletId: id });
  }

  async updateStatus(id: number, status: OutletStatus): Promise<Outlet> {
    apiLogger.info('Updating outlet status', { outletId: id, status });
    const updatedOutlet = await this.outletRepo.update(id, { isActive: status.isActive });
    if (!updatedOutlet) {
      throw new NotFoundError('Outlet not found');
    }
    apiLogger.info('Outlet status updated successfully', { outletId: id, isActive: status.isActive });
    return updatedOutlet;
  }

  async getActive(): Promise<ActiveOutlet[]> {
    apiLogger.info('Fetching active outlets');
    try {
      return await this.outletRepo.findAllActive();
    } catch (error) {
      apiLogger.error('Error fetching active outlets:', error);
      throw new AppError('Failed to retrieve active outlets.');
    }
  }

  async getStats(): Promise<any> {
    apiLogger.info('Fetching outlet stats');
    try {
      return await this.outletRepo.getStats();
    } catch (error) {
      apiLogger.error('Error fetching outlet stats:', error);
      throw new AppError('Failed to retrieve outlet stats.');
    }
  }

  async getOutletStatsSummary(): Promise<any> {
    apiLogger.info('Fetching outlet statistics summary');
    try {
      return await this.outletRepo.getStats();
    } catch (error) {
      apiLogger.error('Error fetching outlet statistics summary:', error);
      throw new AppError('Failed to retrieve outlet statistics summary.');
    }
  }

  async bulkAction(action: BulkOutletAction['action'], outletIds: number[]): Promise<any> {
    apiLogger.info('Performing bulk action on outlets', { action, outletIds });
    let successCount = 0;
    const results = [];

    for (const outletId of outletIds) {
      try {
        let success = false;
        switch (action) {
          case 'activate':
            success = !!(await this.outletRepo.update(outletId, { isActive: true }));
            break;
          case 'deactivate':
            success = !!(await this.outletRepo.update(outletId, { isActive: false }));
            break;
          case 'delete':
            success = await this.outletRepo.softDelete(outletId);
            break;
        }
        if (success) successCount++;
        results.push({ outletId, success, error: null });
      } catch (error) {
        results.push({
          outletId,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    apiLogger.info('Bulk action completed', { action, total: outletIds.length, successful: successCount });
    return {
      message: `Bulk ${action} completed. ${successCount}/${outletIds.length} operations successful.`,
      results,
    };
  }
}

export const outletService = new OutletService();